<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Income Create', 'Income Read', 'Expense Create', 'Expense Read'])): ?>
    <li class="menu-item <?php echo e(request()->is('accounts/income_expense*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-calculator"></i>
            <div data-i18n="Income & Expense"><?php echo e(__('Income & Expense')); ?></div>
        </a>
        <ul class="menu-sub">
            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/statistics*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.accounts.income_expense.statistics.index')); ?>" class="menu-link"><?php echo e(__('Statistics')); ?></a>
            </li>

            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/category/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.accounts.income_expense.category.index')); ?>" class="menu-link"><?php echo e(__('Categories')); ?></a>
            </li>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Income Create', 'Income Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('accounts/income_expense/income*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Income"><?php echo e(__('Income')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Income Read')): ?>
                            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/income/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.accounts.income_expense.income.index')); ?>" class="menu-link">
                                    <div data-i18n="All Incomes"><?php echo e(__('All Incomes')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Income Create')): ?>
                            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/income/create') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.accounts.income_expense.income.create')); ?>" class="menu-link">
                                    <div data-i18n="Create Income"><?php echo e(__('Create Income')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Expense Create', 'Expense Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('accounts/income_expense/expense*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Expense"><?php echo e(__('Expense')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Expense Read')): ?>
                            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/expense/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.accounts.income_expense.expense.index')); ?>" class="menu-link">
                                    <div data-i18n="All Expenses"><?php echo e(__('All Expenses')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Expense Create')): ?>
                            <li class="menu-item <?php echo e(request()->is('accounts/income_expense/expense/create') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.accounts.income_expense.expense.create')); ?>" class="menu-link">
                                    <div data-i18n="Create Expense"><?php echo e(__('Create Expense')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/income_expense.blade.php ENDPATH**/ ?>