<div class="card card-action mb-4">
    <div class="card-header align-items-center">
        <h5 class="card-action-title mb-0">Task Assignees</h5>
        <?php if(auth()->user()->id == $task->creator->id): ?>
            <div class="card-action-element">
                <div class="dropdown">
                    <button type="button" class="btn dropdown-toggle hide-arrow p-0" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ti ti-dots-vertical text-muted"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#addTaskUsersModal">
                                <i class="ti ti-plus me-1 fs-5" style="margin-top: -2px;"></i>
                                Add Assignees
                            </button>
                        </li>
                        <li>
                            <hr class="dropdown-divider" />
                        </li>
                        <li>
                            <button type="button" class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#removeTaskUserModal">
                                <i class="ti ti-x me-1 fs-5" style="margin-top: -2px;"></i>
                                Remove Assignees
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <div class="card-body">
        <ul class="list-unstyled mb-0">
            <?php $__currentLoopData = $task->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="mb-3">
                    <div class="d-flex align-items-start">
                        <div class="d-flex align-items-start">
                            <div class="avatar me-2">
                                <?php if($user->hasMedia('avatar')): ?>
                                    <img src="<?php echo e($user->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="Avatar" class="rounded-circle">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('assets/img/avatars/no_image.png')); ?>" alt="No Avatar" class="rounded-circle">
                                <?php endif; ?>
                            </div>
                            <div class="me-2 ms-1">
                                <h6 class="mb-0"><?php echo e($user->alias_name); ?></h6>
                                <small class="text-muted fs-tiny"><?php echo e(show_date($user->pivot->created_at)); ?></small>
                            </div>
                        </div>
                        <div class="ms-auto">
                            <b class="text-muted"><?php echo e($user->pivot->progress); ?>%</b>
                        </div>
                    </div>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
</div>



<?php echo $__env->make('administration.task.modals.add_assignees', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php echo $__env->make('administration.task.modals.remove_assignee', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/task_assignees.blade.php ENDPATH**/ ?>