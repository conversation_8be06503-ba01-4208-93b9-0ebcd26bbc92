<div class="row">
    <div class="col-md-12">
        <div class="card card-border-shadow-primary mb-4 border-0">
            <div class="card-body row">
                <div class="col-md-8 card-separator">
                    <div class="d-flex justify-content-between flex-wrap gap-3 me-3">
                        <div class="d-flex align-items-center gap-3 me-4 me-sm-0">
                            <span class="bg-label-success p-2 rounded">
                                <i class="ti ti-briefcase ti-xl"></i>
                            </span>
                            <div class="content-right">
                                <h5 class="text-success mb-0" title="<?php echo e(__('Total Worked')); ?>" data-bs-placement="left"><?php echo e(format_number($totalWorkedDays)); ?> <small>Days</small></h5>
                                <small class="mb-0" title="<?php echo e(__('Total Days in '). config('app.name')); ?>" data-bs-placement="left"><?php echo e(total_day($user->employee->joining_date)); ?></small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <span class="bg-label-primary p-2 rounded">
                                <i class="ti ti-hourglass-high ti-xl"></i>
                            </span>
                            <div class="content-right">
                                <h5 class="text-primary mb-0"><?php echo e(total_time($totalRegularWork)); ?></h5>
                                <small class="mb-0 text-muted">Total Worked (Regular)</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <span class="bg-label-warning p-2 rounded">
                                <i class="ti ti-hourglass-low ti-xl"></i>
                            </span>
                            <div class="content-right">
                                <h5 class="text-warning mb-0"><?php echo e(total_time($totalOvertimeWork)); ?></h5>
                                <small class="mb-0 text-muted">Total Worked (Overtime)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="d-flex justify-content-between flex-wrap gap-3 px-2">
                        <div class="d-flex align-items-center">
                            <div class="avatar flex-shrink-0 me-2">
                                <span class="avatar-initial rounded bg-label-primary">
                                    <i class="ti ti-calendar-time ti-md"></i>
                                </span>
                            </div>
                            <div>
                                <h5 class="mb-0 text-nowrap live-time text-bold" id="liveTime"></h5>
                                <small><?php echo e(date('jS M, Y (l)')); ?></small>
                            </div>
                        </div>
                        <?php if(auth()->user()->hasAllPermissions(['Attendance Create', 'Attendance Read'])): ?>
                            <?php if(isset($activeAttendance->clock_in)): ?>
                                <div class="d-flex align-items-center">
                                    <form action="<?php echo e(route('administration.attendance.clockout')); ?>" method="post" class="confirm-form-danger">
                                        <div class="avatar flex-shrink-0 me-2">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" name="attendance" value="clock_out" class="avatar-initial rounded bg-danger border-0" data-bs-placement="top" title="Clock Out?">
                                                <i class="ti ti-clock-off ti-md"></i>
                                            </button>
                                        </div>
                                    </form>
                                    <div>
                                        <h5 class="mb-0 text-nowrap live-working-time text-<?php echo e($activeAttendance->type == 'Regular' ? 'primary' : 'warning'); ?>"
                                            id="liveWorkingTime"
                                            data-clock-in-at="<?php echo e($activeAttendance->clock_in->timestamp); ?>">
                                        </h5>
                                        <small class="bg-label-<?php echo e($activeAttendance->type == 'Regular' ? 'primary' : 'warning'); ?> p-1 px-2 rounded-2 text-bold"><?php echo e($activeAttendance->type); ?></small>
                                    </div>
                                </div>
                            <?php else: ?>
                                <form action="<?php echo e(route('administration.attendance.clockin')); ?>" method="post">
                                    <?php echo csrf_field(); ?>
                                    <div class="d-flex align-items-center mt-1">
                                        <div class="avatar flex-shrink-0 me-2">
                                            <button type="button" class="avatar-initial rounded bg-primary border-0 submit-regular" data-bs-placement="top" title="Regular Clockin?">
                                                <i class="ti ti-clock-check ti-md"></i>
                                            </button>
                                        </div>
                                        <div class="avatar flex-shrink-0 me-2">
                                            <button type="button" class="avatar-initial rounded bg-warning border-0 submit-overtime" data-bs-placement="top" title="Overtime Clockin?">
                                                <i class="ti ti-clock-check ti-md"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Hidden input to store attendance type -->
                                    <input type="hidden" name="attendance" id="attendanceType">
                                </form>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/dashboard/partials/_attendance_summary.blade.php ENDPATH**/ ?>