<div class="card mb-4 <?php echo e($task->parent_task ? 'border border-warning' : ''); ?>">
    <div class="card-body">
        <small class="card-text text-uppercase d-flex justify-content-between align-items-center">
            <span><?php echo e(__('About Task')); ?></span>
            <?php if($task->parent_task): ?>
                <b class="badge bg-label-warning"><?php echo e(__('Sub Task')); ?></b>
            <?php endif; ?>
        </small>
        <ul class="list-unstyled mb-0 mt-3">
            <?php if($task->parent_task): ?>
                <li class="d-flex align-items-center mb-3">
                    <i class="ti ti-brand-stackshare text-heading"></i>
                    <span class="fw-medium mx-2 text-heading"><?php echo e(__('Parent Task')); ?>:</span>
                    <a href="<?php echo e(route('administration.task.show', ['task' => $task->parent_task, 'taskid' => $task->parent_task->taskid])); ?>" target="_blank" class="text-bold text-primary" title="<?php echo e($task->parent_task->title); ?>"><?php echo e(show_content($task->parent_task->title, 30)); ?></a>
                </li>
            <?php endif; ?>
            <li class="d-flex align-items-center mb-3">
                <i class="ti ti-user-edit text-heading"></i>
                <span class="fw-medium mx-2 text-heading">Creator:</span>
                <span class="text-dark text-bold"><?php echo e($task->creator->alias_name); ?></span>
            </li>
            <li class="d-flex align-items-center mb-3">
                <i class="ti ti-hourglass-off text-heading"></i>
                <span class="fw-medium mx-2 text-heading">Deadline:</span>
                <span class="text-capitalize">
                    <?php if(!is_null($task->deadline)): ?>
                        <?php echo e(show_date($task->deadline)); ?>

                        <?php
                            $deadlineStatus = task_deadline_status($task->deadline, $task->created_at);
                        ?>
                        <span class="badge <?php echo e($deadlineStatus['badge_class']); ?> fw-bold"><?php echo e($deadlineStatus['text']); ?></span>
                    <?php else: ?>
                        <span class="badge bg-success fw-bold"><?php echo e(__('Ongoing Task')); ?></span>
                    <?php endif; ?>
                </span>
            </li>
            <li class="d-flex align-items-center mb-3">
                <i class="ti ti-check text-heading"></i>
                <span class="fw-medium mx-2 text-heading">Status:</span>
                <span class="badge bg-<?php echo e(getColor($task->status)); ?>"><?php echo e($task->status); ?></span>
            </li>
            <li class="d-flex align-items-center mb-3">
                <i class="ti ti-checks text-heading"></i>
                <span class="fw-medium mx-2 text-heading">Priority:</span>
                <span class="badge bg-<?php echo e(getColor($task->priority)); ?>"><?php echo e($task->priority); ?></span>
            </li>
            <?php if(isset($task->chatting)): ?>
                <li class="d-flex align-items-center mb-3">
                    <i class="ti ti-message text-heading"></i>
                    <span class="fw-medium mx-2 text-heading">Chatting:</span>
                    <?php if($task->chatting->sender_id == auth()->user()->id): ?>
                        <a href="<?php echo e(route('administration.chatting.show', ['user' => $task->chatting->receiver->id, 'userid' => $task->chatting->receiver->userid])); ?>" target="_blank">
                            <span class="text-bold"><?php echo e($task->chatting->receiver->alias_name); ?></span>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('administration.chatting.show', ['user' => $task->chatting->sender->id, 'userid' => $task->chatting->sender->userid])); ?>" target="_blank">
                            <span class="text-bold"><?php echo e($task->chatting->sender->alias_name); ?></span>
                        </a>
                    <?php endif; ?>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/about_task.blade.php ENDPATH**/ ?>