<!-- Quiz Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Create', 'Quiz Read', 'Quiz Update', 'Quiz Delete'])): ?>
    <li class="menu-item <?php echo e(request()->is('quiz*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-brain"></i>
            <div data-i18n="Quiz"><?php echo e(__('Quiz')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('quiz/question/all*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.quiz.question.index')); ?>" class="menu-link"><?php echo e(__('All Questions')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Quiz Create')): ?>
                <li class="menu-item <?php echo e(request()->is('quiz/question/create*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.quiz.question.create')); ?>" class="menu-link"><?php echo e(__('Create Question')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Create', 'Quiz Read', 'Quiz Update', 'Quiz Delete'])): ?>
                <li class="menu-item <?php echo e(request()->is('quiz/test*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Quiz Tests"><?php echo e(__('Quiz Tests')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Read'])): ?>
                            <li class="menu-item <?php echo e(request()->is('quiz/test/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.quiz.test.index')); ?>" class="menu-link">
                                    <div data-i18n="All Tests"><?php echo e(__('All Tests')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Create'])): ?>
                            <li class="menu-item <?php echo e(request()->is('quiz/test/create*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.quiz.test.create')); ?>" class="menu-link">
                                    <div data-i18n="Assign Test"><?php echo e(__('Assign Test')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/quiz.blade.php ENDPATH**/ ?>