<div class="card mb-4">
    <div class="card-header header-elements pt-3 pb-3">
        <h5 class="mb-0">Task Files</h5>

        <?php if(auth()->user()->id == $task->creator->id): ?>
            <div class="card-header-elements ms-auto">
                <button type="button" class="btn btn-xs btn-primary" title="Click to upload files for <?php echo e($task->title); ?>" data-bs-toggle="modal" data-bs-target="#addTaskFilesModal">
                    <span class="tf-icon ti ti-upload ti-xs me-1"></span>
                    Upload Files
                </button>
            </div>
        <?php endif; ?>
    </div>

    <?php if($task->files->count() > 0): ?>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Size</th>
                            <th>Uploaded</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $task->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <?php if(in_array($file->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])): ?>
                                        <div class="task-image-container">
                                            <a href="<?php echo e(file_media_download($file)); ?>" data-lightbox="task-images" data-title="<?php echo e($file->original_name); ?>">
                                                <img src="<?php echo e(file_media_download($file)); ?>" alt="<?php echo e($file->original_name); ?>" class="img-fluid img-thumbnail" style="width: 150px; height: 100px; object-fit: cover;">
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="file-thumbnail-container">
                                            <i class="ti ti-file-download fs-2 mb-2 text-primary"></i>
                                            <span class="file-name text-center small fw-medium" title="<?php echo e($file->original_name); ?>">
                                                <?php echo e(show_content($file->original_name, 15)); ?>

                                            </span>
                                            <small class="text-muted"><?php echo e(strtoupper(pathinfo($file->original_name, PATHINFO_EXTENSION))); ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e(get_file_media_size($file)); ?></td>
                                <td><?php echo e(date_time_ago($file->created_at)); ?></td>
                                <td class="text-center">
                                    <?php if($task->creator_id == auth()->user()->id): ?>
                                        <a href="<?php echo e(file_media_destroy($file)); ?>" class="btn btn-icon btn-label-danger btn-sm waves-effect confirm-danger" title="Delete <?php echo e($file->original_name); ?>">
                                            <i class="ti ti-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo e(file_media_download($file)); ?>" target="_blank" class="btn btn-icon btn-primary btn-sm waves-effect" title="Download <?php echo e($file->original_name); ?>">
                                        <i class="ti ti-download"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>



<?php echo $__env->make('administration.task.modals.add_task_files', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/task_files.blade.php ENDPATH**/ ?>