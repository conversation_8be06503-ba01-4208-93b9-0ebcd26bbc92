<!-- Leave History Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Leave History Create', 'Leave History Read'])): ?>
<li class="menu-item <?php echo e(request()->is('leave*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-calendar-pause"></i>
        <div data-i18n="Leave History"><?php echo e(__('Leave')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Leave History Update', 'Leave History Delete'])): ?>
            <li class="menu-item <?php echo e(request()->is('leave/history/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.leave.history.index')); ?>" class="menu-link"><?php echo e(__('All Leaves')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Leave History Read')): ?>
            <li class="menu-item <?php echo e(request()->is('leave/history/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.leave.history.my')); ?>" class="menu-link"><?php echo e(__('My Leaves')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Leave History Create')): ?>
            <li class="menu-item <?php echo e(request()->is('leave/history/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.leave.history.create')); ?>" class="menu-link"><?php echo e(__('Apply For Leave')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/leave.blade.php ENDPATH**/ ?>