<!-- Attendance Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Attendance Create', 'Attendance Read'])): ?>
<li class="menu-item <?php echo e(request()->is('attendance*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-clock-2"></i>
        <div data-i18n="Attendance"><?php echo e(__('Attendance')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Attendance Update', 'Attendance Delete'])): ?>
            <li class="menu-item <?php echo e(request()->is('attendance/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.attendance.index')); ?>" class="menu-link"><?php echo e(__('All Attendances')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Attendance Read')): ?>
            <li class="menu-item <?php echo e(request()->is('attendance/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.attendance.my')); ?>" class="menu-link"><?php echo e(__('My Attendances')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Attendance Everything')): ?>
            <li class="menu-item <?php echo e(request()->is('attendance/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.attendance.create')); ?>" class="menu-link"><?php echo e(__('Assign Attendance')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Attendance Create')): ?>
            <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['Developer'])): ?>
                <li class="menu-item <?php echo e(request()->is('attendance/qrcode*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.attendance.qrcode.scanner')); ?>" class="menu-link"><?php echo e(__('QR Code Attendance')); ?></a>
                </li>
            <?php endif; ?>

            <li class="menu-item <?php echo e(request()->is('attendance/barcode*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.attendance.barcode.scanner')); ?>" class="menu-link"><?php echo e(__('Bar Code Attendance')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Attendance Everything', 'Attendance Read', 'Attendance Update'])): ?>
            <li class="menu-item <?php echo e(request()->is('attendance/issue*') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div data-i18n="Attendance Issue"><?php echo e(__('Attendance Issue')); ?></div>
                </a>
                <ul class="menu-sub">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Attendance Update')): ?>
                        <li class="menu-item <?php echo e(request()->is('attendance/issue/all*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('administration.attendance.issue.index')); ?>" class="menu-link">
                                <div data-i18n="All Issues"><?php echo e(__('All Issues')); ?></div>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Attendance Read')): ?>
                        <li class="menu-item <?php echo e(request()->is('attendance/issue/my*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('administration.attendance.issue.my')); ?>" class="menu-link">
                                <div data-i18n="My Issues"><?php echo e(__('My Issues')); ?></div>
                            </a>
                        </li>
                        <li class="menu-item <?php echo e(request()->is('attendance/issue/create*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('administration.attendance.issue.create')); ?>" class="menu-link">
                                <div data-i18n="Create Issue"><?php echo e(__('Create Issue')); ?></div>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/attendance.blade.php ENDPATH**/ ?>