<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card card-border-shadow-primary">
            <div class="card-header header-elements">
                <h5 class="mb-0">My Attendances Of <b class="text-bold text-primary"><?php echo e(date('F Y')); ?></b></h5>

                <div class="card-header-elements ms-auto" style="margin-top: -5px;">
                    <small class="badge bg-primary cursor-pointer" title="Total Working Hour (Regular)" data-bs-placement="top" >
                        <?php echo e(total_time($totalRegularWorkingHour)); ?>

                    </small>

                    <?php if(isset($totalOvertimeWorkingHour)): ?>
                        <small class="badge bg-warning cursor-pointer" title="Total Working Hour (Overtime)" data-bs-placement="bottom" >
                            <?php echo e(total_time($totalOvertimeWorkingHour)); ?>

                        </small>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive text-nowrap">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th class="text-left">Date</th>
                                <th class="text-center">Clock In</th>
                                <th class="text-center">Clock Out</th>
                                <th class="text-center">Total Worked</th>
                                <th class="text-center">Total Break</th>
                                <th class="text-center">Over Break</th>
                                <th class="text-right">Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $attendances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="text-left">
                                        <span class="fw-medium"><?php echo e(show_date_month_day($attendance->clock_in_date)); ?></span>
                                    </td>
                                    <td class="text-center"><?php echo e(show_time($attendance->clock_in)); ?></td>
                                    <td class="text-center">
                                        <?php if(isset($attendance->clock_out)): ?>
                                            <span><?php echo e(show_time($attendance->clock_out)); ?></span>
                                        <?php else: ?>
                                            <span class="text-bold text-success">Running</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if(isset($attendance->total_time)): ?>
                                            <span><?php echo e(total_time($attendance->total_time)); ?></span>
                                        <?php else: ?>
                                            <span class="text-bold text-success">Running</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if(isset($attendance->total_break_time)): ?>
                                            <span><?php echo e(total_time($attendance->total_break_time)); ?></span>
                                        <?php else: ?>
                                            <span class="text-bold text-success" title="No Break Taken" data-bs-placement="right">
                                                <i class="ti ti-clock-play"></i>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if(isset($attendance->total_over_break)): ?>
                                            <span class="text-danger"><?php echo e(total_time($attendance->total_over_break)); ?></span>
                                        <?php else: ?>
                                            <span class="text-bold text-success" title="No Over Break" data-bs-placement="right">
                                                <i class="ti ti-mood-check"></i>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-right">
                                        <?php if($attendance->type == 'Regular'): ?>
                                            <span class="badge bg-label-primary me-1">Regular</span>
                                        <?php else: ?>
                                            <span class="badge bg-label-warning me-1">Overtime</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/dashboard/partials/_running_month_attendance.blade.php ENDPATH**/ ?>