<div class="card mb-4">
    <div class="card-header header-elements">
        <h5 class="mb-0">Task Comments</h5>

        <div class="card-header-elements ms-auto">
            
        </div>
    </div>
    <!-- Account -->
    <div class="card-body">
        <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
            <div class="row">
                <div class="col-md-12">
                    <form action="<?php echo e(route('administration.task.comment.store', ['task' => $task])); ?>" method="post" enctype="multipart/form-data" autocomplete="off" id="taskCommentForm">
                        <?php echo csrf_field(); ?>
                        <div class="collapse show" id="taskComment">
                            <div class="row">
                                <div class="mb-3 col-md-12">
                                    <div name="comment" id="taskCommentEditor"><?php echo old('comment'); ?></div>
                                    <textarea class="d-none" name="comment" id="commentInput"><?php echo e(old('comment')); ?></textarea>
                                    <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <b class="text-danger"><?php echo e($message); ?></b>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-12 mb-1">
                                    <input type="file" id="comment-files" name="files[]" value="<?php echo e(old('files[]')); ?>" placeholder="<?php echo e(__('Task Comment Files')); ?>" class="form-control <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple/>
                                    <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary btn-sm btn-block mt-2 mb-3">
                                        <i class="ti ti-check"></i>
                                        Submit Comment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-12 comments">
                <?php
                    $senderColor = 'background-color: #f0676714 !important; border: 1px solid #f067675c !important;';
                    $receiverColor = 'background-color: #7367f014 !important; border: 1px solid #7367f05c !important;';
                ?>

                <?php $__currentLoopData = $task->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="comment-container mb-4 p-3 rounded chat-message" id="comment-<?php echo e($comment->id); ?>" style="<?php echo e($comment->commenter->id == auth()->user()->id ? $senderColor : $receiverColor); ?>">
                        <!-- Main Comment -->
                        <div class="main-comment">
                            <div class="d-flex justify-content-between align-items-center user-name mb-2">
                                <?php echo show_user_name_and_avatar($comment->commenter, name: false); ?>

                                <small class="date-time text-muted"><?php echo e(date_time_ago($comment->created_at)); ?></small>
                            </div>

                            <div class="comment-content mb-2">
                                <?php echo $comment->comment; ?>

                            </div>

                            <?php if($comment->files->count() > 0): ?>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <?php $__currentLoopData = $comment->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $commentFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(in_array($commentFile->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])): ?>
                                            <div class="comment-image-container" title="Click to view <?php echo e($commentFile->original_name); ?>">
                                                <a href="<?php echo e(file_media_download($commentFile)); ?>" data-lightbox="comment-images-<?php echo e($comment->id); ?>" data-title="<?php echo e($commentFile->original_name); ?>">
                                                    <img src="<?php echo e(file_media_download($commentFile)); ?>" alt="<?php echo e($commentFile->original_name); ?>" class="img-fluid img-thumbnail" style="width: 150px; height: 100px; object-fit: cover;">
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <div class="file-thumbnail-container" title="Click to Download <?php echo e($commentFile->original_name); ?>">
                                                <a href="<?php echo e(file_media_download($commentFile)); ?>" target="_blank" class="text-decoration-none">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="ti ti-file-download fs-2 mb-2 text-primary"></i>
                                                        <span class="file-name text-center small fw-medium">
                                                            <?php echo e(show_content($commentFile->original_name, 15)); ?>

                                                        </span>
                                                        <small class="text-muted"><?php echo e(strtoupper(pathinfo($commentFile->original_name, PATHINFO_EXTENSION))); ?></small>
                                                    </div>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Reply Button -->
                            <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
                                <div class="d-flex justify-content-end mb-2">
                                    <a href="javascript:void(0);" class="text-primary text-bold reply-btn" data-comment-id="<?php echo e($comment->id); ?>" title="Reply">
                                        <i class="ti ti-arrow-back-up me-1"></i>
                                        Reply
                                    </a>
                                </div>
                            <?php endif; ?>

                            <!-- Reply Form -->
                            <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
                                <div class="reply-form-container" id="replyForm-<?php echo e($comment->id); ?>" style="display: none;">
                                    <form action="<?php echo e(route('administration.task.comment.store', ['task' => $task])); ?>" method="post" enctype="multipart/form-data" autocomplete="off" class="reply-form">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="parent_comment_id" value="<?php echo e($comment->id); ?>">
                                        <div class="row">
                                            <div class="mb-3 col-md-12">
                                                <div class="reply-editor" id="replyEditor-<?php echo e($comment->id); ?>"></div>
                                                <textarea class="d-none reply-input" name="comment"></textarea>
                                            </div>
                                            <div class="col-md-12 mb-1">
                                                <input type="file" name="files[]" class="form-control" multiple/>
                                            </div>
                                            <div class="col-md-12">
                                                <button type="submit" class="btn btn-primary btn-sm me-2">
                                                    <i class="ti ti-check"></i>
                                                    Submit Reply
                                                </button>
                                                <button type="button" class="btn btn-secondary btn-sm cancel-reply" data-comment-id="<?php echo e($comment->id); ?>">
                                                    Cancel
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Replies -->
                        <?php if($comment->replies->count() > 0): ?>
                            <div class="replies-container mt-3">
                                <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="reply-item mb-3 p-3 rounded chat-reply" id="comment-<?php echo e($reply->id); ?>" style="background-color: rgba(115, 103, 240, 0.05); border-left: 3px solid #7367f0; margin-left: 20px;">
                                        <!-- Parent Comment Preview -->
                                        <?php
                                            // Get the parent comment (could be main comment or another reply)
                                            $parentComment = $reply->parent_comment;
                                            $isReplyToMainComment = $parentComment->parent_comment_id === null;
                                        ?>
                                        <div class="parent-comment-preview mb-2 p-2 rounded" style="background-color: rgba(0,0,0,0.05); border-left: 2px solid #dee2e6; cursor: pointer;" onclick="scrollToComment(<?php echo e($parentComment->id); ?>)">
                                            <div class="d-flex align-items-center mb-1">
                                                <?php echo show_user_name_and_avatar($parentComment->commenter, name: false, avatar: false); ?>

                                                <small class="text-muted ms-2">
                                                    <i class="ti ti-corner-down-left me-1"></i>
                                                    Replying to <?php echo e($isReplyToMainComment ? 'this comment' : 'this reply'); ?>

                                                </small>
                                            </div>
                                            <div class="text-muted small" style="line-height: 1.2;">
                                                <?php
                                                    $truncatedComment = strip_tags($parentComment->comment);
                                                    $truncatedComment = show_content($truncatedComment, 80);
                                                ?>
                                                <?php echo e($truncatedComment); ?>

                                            </div>
                                        </div>

                                        <!-- Reply Header -->
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <?php echo show_user_name_and_avatar($reply->commenter, name: false); ?>

                                            <small class="date-time text-muted"><?php echo e(date_time_ago($reply->created_at)); ?></small>
                                        </div>

                                        <!-- Reply Content -->
                                        <div class="reply-content mb-2">
                                            <?php echo $reply->comment; ?>

                                        </div>

                                        <!-- Reply Files -->
                                        <?php if($reply->files->count() > 0): ?>
                                            <div class="d-flex flex-wrap gap-2 mb-3">
                                                <?php $__currentLoopData = $reply->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $replyFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(in_array($replyFile->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])): ?>
                                                        <div class="comment-image-container" title="Click to view <?php echo e($replyFile->original_name); ?>">
                                                            <a href="<?php echo e(file_media_download($replyFile)); ?>" data-lightbox="comment-images-<?php echo e($reply->id); ?>" data-title="<?php echo e($replyFile->original_name); ?>">
                                                                <img src="<?php echo e(file_media_download($replyFile)); ?>" alt="<?php echo e($replyFile->original_name); ?>" class="img-fluid img-thumbnail" style="width: 150px; height: 100px; object-fit: cover;">
                                                            </a>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="file-thumbnail-container" title="Click to Download <?php echo e($replyFile->original_name); ?>">
                                                            <a href="<?php echo e(file_media_download($replyFile)); ?>" target="_blank" class="text-decoration-none">
                                                                <div class="d-flex flex-column align-items-center">
                                                                    <i class="ti ti-file-download fs-2 mb-2 text-primary"></i>
                                                                    <span class="file-name text-center small fw-medium">
                                                                        <?php echo e(show_content($replyFile->original_name, 15)); ?>

                                                                    </span>
                                                                    <small class="text-muted"><?php echo e(strtoupper(pathinfo($replyFile->original_name, PATHINFO_EXTENSION))); ?></small>
                                                                </div>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Reply to Reply Button -->
                                        <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
                                            <div class="d-flex justify-content-end mb-2">
                                                <a href="javascript:void(0);" class="text-primary text-bold reply-btn" data-comment-id="<?php echo e($reply->id); ?>" title="Reply to this reply">
                                                    <i class="ti ti-arrow-back-up me-1"></i>
                                                    Reply
                                                </a>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Reply to Reply Form -->
                                        <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
                                            <div class="reply-form-container" id="replyForm-<?php echo e($reply->id); ?>" style="display: none;">
                                                <form action="<?php echo e(route('administration.task.comment.store', ['task' => $task])); ?>" method="post" enctype="multipart/form-data" autocomplete="off" class="reply-form">
                                                    <?php echo csrf_field(); ?>
                                                    <input type="hidden" name="parent_comment_id" value="<?php echo e($reply->id); ?>">
                                                    <div class="row">
                                                        <div class="mb-3 col-md-12">
                                                            <div class="reply-editor" id="replyEditor-<?php echo e($reply->id); ?>"></div>
                                                            <textarea class="d-none reply-input" name="comment"></textarea>
                                                        </div>
                                                        <div class="col-md-12 mb-1">
                                                            <input type="file" name="files[]" class="form-control" multiple/>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <button type="submit" class="btn btn-primary btn-sm me-2">
                                                                <i class="ti ti-check"></i>
                                                                Submit Reply
                                                            </button>
                                                            <button type="button" class="btn btn-secondary btn-sm cancel-reply" data-comment-id="<?php echo e($reply->id); ?>">
                                                                Cancel
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to scroll to a specific comment
    function scrollToComment(commentId) {
        const commentElement = document.getElementById('comment-' + commentId);
        if (commentElement) {
            // Add highlight effect
            commentElement.style.transition = 'all 0.3s ease';
            commentElement.style.boxShadow = '0 0 15px rgba(115, 103, 240, 0.5)';

            // Scroll to the comment
            commentElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Remove highlight after 2 seconds
            setTimeout(() => {
                commentElement.style.boxShadow = '';
            }, 2000);
        }
    }
</script>


<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/task_comments.blade.php ENDPATH**/ ?>