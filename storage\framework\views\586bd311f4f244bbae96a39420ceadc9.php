<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Task')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
        .more-user-avatar {
            background-color: #dddddd;
            border-radius: 50px;
            text-align: center;
            padding-top: 5px;
            border: 1px solid #ffffff;
        }
        .more-user-avatar small {
            font-size: 12px;
            color: #333333;
            font-weight: bold;
        }
        .list-group-item + .list-group-item {
            border-top-width: 1px;
        }

        .grid-view {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Responsive Grid */
            gap: 20px; /* Spacing between items */
        }
        .grid-view .list-group-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 15px;
            border-radius: 10px;
            transition: transform 0.2s ease-in-out;
        }
        .grid-view .list-group-item:hover {
            transform: translateY(-5px); /* Subtle hover effect */
        }
        .grid-view .list-content {
            width: 100%;
        }
        .grid-view .li-wrapper.li-task-status-priority {
            position: absolute;
            right: 0;
            top: 0;
        }
        .grid-view .task-status {
            position: absolute;
            right: 10px;
            top: 40px;
        }
        .grid-view .task-priority {
            position: absolute;
            right: 10px;
            top: 10px;
        }
        .grid-view .li-wrapper {
            margin-bottom: 15px;
        }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('My Tasks')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Task')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('My Tasks')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->

<div class="row">
    <div class="col-md-12">
        <form action="<?php echo e(route('administration.task.my')); ?>" method="get">
            <?php echo csrf_field(); ?>
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-7">
                            <label for="creator_id" class="form-label">Select Task Creator</label>
                            <select name="creator_id" id="creator_id" class="select2 form-select <?php $__errorArgs = ['creator_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->creator_id) ? 'selected' : ''); ?>>Select Creator</option>
                                <?php $__currentLoopData = $creators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $creator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($creator->id); ?>" <?php echo e($creator->id == request()->creator_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($creator)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['creator_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-5">
                            <label for="status" class="form-label">Select Task Status</label>
                            <select name="status" id="status" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                <option value="" <?php echo e(is_null(request()->status) ? 'selected' : ''); ?>>Select Status</option>
                                <option value="Active" <?php echo e(request()->status == 'Active' ? 'selected' : ''); ?>>Active</option>
                                <option value="Running" <?php echo e(request()->status == 'Running' ? 'selected' : ''); ?>>Running</option>
                                <option value="Completed" <?php echo e(request()->status == 'Completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="Cancelled" <?php echo e(request()->status == 'Cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="col-md-12 text-end">
                        <?php if(request()->creator_id || request()->user_id || request()->status): ?>
                            <a href="<?php echo e(route('administration.task.my')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                Reset Filters
                            </a>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            Filter Tasks
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">My Tasks <sup class="text-muted">(Assigned to me / Created by me)</sup></h5>
                <div class="card-header-elements ms-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Task Create')): ?>
                        <a href="<?php echo e(route('administration.task.create')); ?>" class="btn btn-sm btn-primary">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Create Task
                        </a>
                    <?php endif; ?>
                    <button id="toggleView" class="btn btn-icon btn-outline-dark" title="Switch View">
                        <span class="tf-icon ti ti-layout-2"></span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="vehicles-overview-progress progress rounded-0 mb-2" style="height: 40px">
                    <?php if($statusPercentages['active'] > 0): ?>
                        <div class="progress-bar fw-medium text-start bg-label-info px-3 rounded-0 text-center" role="progressbar"
                            style="width: <?php echo e($statusPercentages['active']); ?>%" aria-valuenow="<?php echo e($statusPercentages['active']); ?>"
                            aria-valuemin="0" aria-valuemax="100" title="Active Tasks">
                            <span class="percentage-value"><?php echo e($statusPercentages['active']); ?>%</span>
                        </div>
                    <?php endif; ?>

                    <?php if($statusPercentages['running'] > 0): ?>
                        <div class="progress-bar fw-medium text-start bg-label-primary px-3 rounded-0 text-center" role="progressbar"
                            style="width: <?php echo e($statusPercentages['running']); ?>%" aria-valuenow="<?php echo e($statusPercentages['running']); ?>"
                            aria-valuemin="0" aria-valuemax="100" title="Running Tasks">
                            <span class="percentage-value"><?php echo e($statusPercentages['running']); ?>%</span>
                        </div>
                    <?php endif; ?>

                    <?php if($statusPercentages['completed'] > 0): ?>
                        <div class="progress-bar fw-medium text-start bg-label-success px-3 rounded-0 text-center" role="progressbar"
                            style="width: <?php echo e($statusPercentages['completed']); ?>%" aria-valuenow="<?php echo e($statusPercentages['completed']); ?>"
                            aria-valuemin="0" aria-valuemax="100" title="Completed Tasks">
                            <span class="percentage-value"><?php echo e($statusPercentages['completed']); ?>%</span>
                        </div>
                    <?php endif; ?>

                    <?php if($statusPercentages['canceled'] > 0): ?>
                        <div class="progress-bar fw-medium text-start bg-label-danger px-3 rounded-0 text-center" role="progressbar"
                            style="width: <?php echo e($statusPercentages['canceled']); ?>%" aria-valuenow="<?php echo e($statusPercentages['canceled']); ?>"
                            aria-valuemin="0" aria-valuemax="100" title="Canceled Tasks">
                            <span class="percentage-value"><?php echo e($statusPercentages['canceled']); ?>%</span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="demo-inline-spacing mt-1">

                            <div id="taskContainer" class="list-group list-view"> 
                                <?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <a href="<?php echo e(route('administration.task.show', ['task' => $task, 'taskid' => $task->taskid])); ?>" class="list-group-item d-flex justify-content-between btn-outline-<?php echo e(getColor($task->status)); ?> bg-label-<?php echo e(getColor($task->status)); ?> mb-3" style="border-radius: 5px;">
                                        <div class="li-wrapper d-flex justify-content-start align-items-center" title="<?php echo e($task->title); ?>">
                                            <div class="list-content">
                                                <h6 class="mb-1 text-dark text-bold text-capitalize"><?php echo e(show_content($task->title, 30)); ?></h6>
                                                <small class="text-muted">Task ID: <b><?php echo e($task->taskid); ?></b></small>
                                            </div>
                                        </div>
                                        <div class="li-wrapper d-flex justify-content-start align-items-center">
                                            <div class="list-content">
                                                <?php if(!is_null($task->deadline)): ?>
                                                    <b class="text-dark" title="Task Deadline"><?php echo e(show_date($task->deadline)); ?></b>
                                                    <?php
                                                        $deadlineStatus = task_deadline_status($task->deadline, $task->created_at);
                                                    ?>
                                                    <br>
                                                    <span class="badge <?php echo e($deadlineStatus['badge_class']); ?> fs-tiny fw-bold" title="Deadline Status"><?php echo e($deadlineStatus['text']); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-success" title="Task Deadline">Ongoing Task</span>
                                                <?php endif; ?>
                                                <?php if($task->parent_task): ?>
                                                    <small class="badge bg-dark mb-1"><?php echo e(__('Sub Task')); ?></small>
                                                <?php else: ?>
                                                    <?php if($task->sub_tasks->count() > 0): ?>
                                                        <small class="badge bg-dark mb-1" title="Total Sub-Tasks"><?php echo e($task->sub_tasks->count()); ?></small>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <br>
                                                <small class="text-dark">Created: <span class="text-muted"><?php echo e(show_date($task->created_at)); ?></span></small>
                                            </div>
                                        </div>
                                        <div class="li-wrapper d-flex justify-content-start align-items-center li-task-status-priority">
                                            <div class="list-content text-center">
                                                <small class="badge bg-<?php echo e(getColor($task->status)); ?> mb-1 task-status" title="Task Status"><?php echo e($task->status); ?></small>
                                                <br>
                                                <small class="badge bg-<?php echo e(getColor($task->priority)); ?> task-priority" title="Task Priority"><?php echo e($task->priority); ?></small>
                                            </div>
                                        </div>
                                        <div class="li-wrapper d-flex justify-content-start align-items-center">
                                            <div class="list-content">
                                                <span class="text-dark text-bold" title="Task Creator">
                                                    <?php echo e($task->creator->alias_name); ?>

                                                </span>
                                                <br>
                                                <?php if($task->users->count() > 0): ?>
                                                    <div class="d-flex align-items-center">
                                                        <ul class="list-unstyled d-flex align-items-center avatar-group mb-0 zindex-2 mt-1">
                                                            <?php $__currentLoopData = $task->users->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="<?php echo e($user->alias_name); ?>" class="avatar avatar-sm pull-up">
                                                                    <?php if($user->hasMedia('avatar')): ?>
                                                                        <img src="<?php echo e($user->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="Avatar" class="rounded-circle">
                                                                    <?php else: ?>
                                                                        <img src="<?php echo e(asset('assets/img/avatars/no_image.png')); ?>" alt="No Avatar" class="rounded-circle">
                                                                    <?php endif; ?>
                                                                </li>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($task->users->count() > 6): ?>
                                                                <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="<?php echo e($task->users->count() - 6); ?> More" class="avatar avatar-sm pull-up more-user-avatar">
                                                                    <small><?php echo e($task->users->count() - 6); ?>+</small>
                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <h4 class="text-center text-muted mt-3"><?php echo e(__('No Tasks Available')); ?></h4>
                                <?php endif; ?>

                                
                            </div>
                        </div>
                    </div>

                    
                    <?php echo pagination($tasks, 'center', 'primary'); ?>

                    
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            var $container = $('#taskContainer');
            var $button = $('#toggleView');

            // Check localStorage for stored view preference, or default to 'list-view'
            var viewMode = localStorage.getItem('taskViewMode');
            if (!viewMode) {
                viewMode = 'list-view'; // Default to list-view if nothing is stored
            }

            // Apply the view mode from localStorage or default
            $container.removeClass('grid-view list-view').addClass(viewMode);
            updateButtonText(viewMode);

            // Toggle View on Button Click
            $button.on('click', function () {
                var newViewMode = $container.hasClass('grid-view') ? 'list-view' : 'grid-view';

                // Update the class for task container
                $container.removeClass('grid-view list-view').addClass(newViewMode);

                // Store the updated view mode in localStorage
                localStorage.setItem('taskViewMode', newViewMode);

                // Update button text/icon based on the new view mode
                updateButtonText(newViewMode);
            });

            // Function to update button text/icon based on the view mode
            function updateButtonText(mode) {
                if (mode === 'grid-view') {
                    $button.html('<span class="tf-icon ti ti-list ti-xs me-1" title="Switch to List View"></span>');
                } else {
                    $button.html('<span class="tf-icon ti ti-layout-2 ti-xs me-1" title="Switch to Grid View"></span>');
                }
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/my.blade.php ENDPATH**/ ?>