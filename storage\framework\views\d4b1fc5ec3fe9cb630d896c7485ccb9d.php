<!-- Task Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Task Everything', 'Task Create', 'Task Read'])): ?>
<li class="menu-item <?php echo e(request()->is('task*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-brand-stackshare"></i>
        <div data-i18n="Task"><?php echo e(__('Task')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Task Everything'])): ?>
            <li class="menu-item <?php echo e(request()->is('task/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.task.index')); ?>" class="menu-link"><?php echo e(__('All Tasks')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Task Read')): ?>
            <li class="menu-item <?php echo e(request()->is('task/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.task.my')); ?>" class="menu-link"><?php echo e(__('My Tasks')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Task Create')): ?>
            <li class="menu-item <?php echo e(request()->is('task/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.task.create')); ?>" class="menu-link"><?php echo e(__('New Task')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/task.blade.php ENDPATH**/ ?>