<!-- Announcement Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Announcement Everything', 'Announcement Create', 'Announcement Read'])): ?>
<li class="menu-item <?php echo e(request()->is('announcement*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-speakerphone"></i>
        <div data-i18n="Announcement"><?php echo e(__('Announcement')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Announcement Everything'])): ?>
            <li class="menu-item <?php echo e(request()->is('announcement/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.announcement.index')); ?>" class="menu-link"><?php echo e(__('All Announcements')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Read')): ?>
            <li class="menu-item <?php echo e(request()->is('announcement/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.announcement.my')); ?>" class="menu-link"><?php echo e(__('My Announcements')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Create')): ?>
            <li class="menu-item <?php echo e(request()->is('announcement/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.announcement.create')); ?>" class="menu-link"><?php echo e(__('New Announcement')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/announcement.blade.php ENDPATH**/ ?>